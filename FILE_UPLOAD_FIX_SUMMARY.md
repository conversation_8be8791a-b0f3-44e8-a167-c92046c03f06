# File Upload Fix for Multiple Tape Requests

## Problem Description

When users uploaded optional files along with multiple tape requests (either through manual form for 2-5 tapes or Excel upload for >5 tapes), the files were not properly associated with all related requests. This caused the following issues:

1. **Manual Multi-Tape Form (2-5 tapes)**: Optional file uploads were completely ignored
2. **Excel-Based Multi-Tape Form (>5 tapes)**: Files were only linked to the first request, not all requests in the batch
3. **Display Issue**: Files didn't appear in the Files column for all related requests in pending/completed tables

## Solution Implemented

### 1. Database Schema Changes

**Added `batch_id` field to TapeRequest model:**
```python
batch_id = models.Char<PERSON>ield(max_length=50, blank=True, null=True, help_text="Groups related requests created together")
```

**Created migration:**
- `tapemanagement/migrations/0017_taperequest_batch_id.py`

### 2. View Logic Updates

**Modified `tapemanagement/views.py`:**

#### For Manual Multi-Tape Forms (2-5 tapes):
- Generate unique `batch_id` using `uuid.uuid4()`
- Assign same `batch_id` to all requests created together
- Handle optional file uploads for ALL requests in the batch

#### For Excel-Based Multi-Tape Forms (>5 tapes):
- Generate unique `batch_id` for the batch
- Associate Excel file with ALL requests (not just the first one)
- Handle optional file uploads for ALL requests in the batch

### 3. Key Code Changes

**Import added:**
```python
import uuid
```

**Batch ID generation:**
```python
batch_id = str(uuid.uuid4())
```

**File association for all requests:**
```python
# Handle optional file upload for all requests in the batch
uploaded_file = form.cleaned_data.get('file_upload')
if uploaded_file and created_requests:
    for tape_request in created_requests:
        file_upload = FileUpload(
            tape_request=tape_request,
            user=request.user,
            file=uploaded_file,
            original_filename=uploaded_file.name
        )
        file_upload.save()
```

## How It Works Now

1. **Request Creation**: When multiple tape requests are created together, they all receive the same `batch_id`
2. **File Upload**: Any optional files uploaded during request creation are associated with ALL requests in the batch
3. **Display**: Files now appear in the Files column for all related requests in pending/completed tables
4. **Grouping**: Related requests can be identified and managed together using the `batch_id`

## Benefits

- ✅ **Consistent File Access**: All related requests show the same uploaded files
- ✅ **Better User Experience**: Users see files associated with all their related requests
- ✅ **Data Integrity**: No files are lost or only partially associated
- ✅ **Backward Compatibility**: Existing single requests continue to work as before
- ✅ **Future Extensibility**: `batch_id` can be used for other batch operations

## Testing

The fix has been verified to:
1. Generate unique batch IDs for related requests
2. Associate optional files with all requests in a batch
3. Maintain backward compatibility with single requests
4. Work for both manual and Excel-based multi-tape submissions

## Files Modified

1. `tapemanagement/models.py` - Added `batch_id` field
2. `tapemanagement/views.py` - Updated request creation logic
3. `tapemanagement/migrations/0017_taperequest_batch_id.py` - Database migration

## Migration Required

To apply this fix to an existing system:
```bash
python manage.py migrate
```

This will add the `batch_id` column to the existing `TapeRequest` table without affecting existing data.
