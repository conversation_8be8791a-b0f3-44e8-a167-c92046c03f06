#!/usr/bin/env python3
"""
Test script to verify that optional file uploads work correctly for multiple tape requests
"""

import os
import sys
import django
from datetime import date
from django.core.files.uploadedfile import SimpleUploadedFile

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tapelibrary_project.settings')
django.setup()

from tapemanagement.models import CustomUser, TapeRequest, FileUpload

def test_batch_file_upload():
    """Test that files are associated with all requests in a batch"""
    
    # Get or create a test user
    user, created = CustomUser.objects.get_or_create(
        cpf_number='12345678901',
        defaults={
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"Created test user: {user.username}")
    else:
        print(f"Using existing test user: {user.username}")
    
    # Create a batch of requests with the same batch_id
    batch_id = "test-batch-123"
    
    # Create multiple requests
    requests = []
    for i in range(3):
        tape_request = TapeRequest.objects.create(
            user=user,
            cpf_number=user.cpf_number,
            username=user.username,
            number_of_tapes='<=5',
            datatype='2D',
            dataset_type='Raw Shot Gather',
            data_format='SEG-Y',
            date=date.today(),
            area=f'Test Area {i+1}',
            survey_name=f'Test Survey {i+1}',
            group='TDMS',
            activity='Test activity',
            input_media_type='LTO',
            input_vendor_id=f'INPUT_{i+1}',
            output_media_type='3592',
            output_vendor_id=f'OUTPUT_{i+1}',
            batch_id=batch_id
        )
        requests.append(tape_request)
        print(f"Created request {tape_request.id} with batch_id: {batch_id}")
    
    # Create a test file upload for each request
    test_file_content = b"This is a test file content for batch upload"
    
    for tape_request in requests:
        file_upload = FileUpload.objects.create(
            tape_request=tape_request,
            user=user,
            file=SimpleUploadedFile(
                name="test_file.txt",
                content=test_file_content,
                content_type="text/plain"
            ),
            original_filename="test_file.txt"
        )
        print(f"Created file upload {file_upload.id} for request {tape_request.id}")
    
    # Verify that all requests have the file
    print("\n=== Verification ===")
    for tape_request in requests:
        files = tape_request.uploads.all()
        print(f"Request {tape_request.id} has {files.count()} file(s):")
        for file_upload in files:
            print(f"  - {file_upload.original_filename}")
    
    # Test the batch query
    batch_requests = TapeRequest.objects.filter(batch_id=batch_id)
    print(f"\nFound {batch_requests.count()} requests with batch_id '{batch_id}'")
    
    # Test file count for the batch
    total_files = FileUpload.objects.filter(tape_request__batch_id=batch_id).count()
    print(f"Total files associated with batch: {total_files}")
    
    print("\n✅ Test completed successfully!")
    return True

if __name__ == '__main__':
    try:
        test_batch_file_upload()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
