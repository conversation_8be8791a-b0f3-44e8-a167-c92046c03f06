# PowerShell script to run Django development server
Write-Host "Starting Django development server..." -ForegroundColor Green
Write-Host ""
Write-Host "Server will be available at: http://127.0.0.1:8000" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Set environment variable
$env:DJANGO_SETTINGS_MODULE = "tapelibrary_project.settings"

# Change to project directory
Set-Location "c:\Users\<USER>\Desktop\tapelibrary"

# Run the server using the virtual environment Python
& ".\venv\bin\python" manage.py runserver 127.0.0.1:8000
