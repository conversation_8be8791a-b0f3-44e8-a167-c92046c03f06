<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPIC Tape Data Management System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --ongc-red: #C41E3A;
            --ongc-orange: #FF6B35;
            --ongc-blue: #003366;
            --ongc-light-blue: #0066CC;
            --ongc-gray: #F8FAFC;
            --text-primary: #1E293B;
            --text-secondary: #64748B;
            --border-color: #E2E8F0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, var(--ongc-light-blue) 100%);
            color: #FFFFFF;
            padding: 2rem 1rem;
            text-align: center;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-family: 'Poppins', sans-serif;
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 600;
            margin: 0;
            color: #FFFFFF;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .main-title {
            font-family: 'Poppins', sans-serif;
            color: var(--ongc-blue);
            text-align: center;
            font-size: clamp(1.8rem, 5vw, 3rem);
            font-weight: 700;
            padding: 3rem 1rem 1rem;
            margin: 0;
            background: linear-gradient(135deg, var(--ongc-blue) 0%, var(--ongc-light-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .intro-text {
            font-family: 'Inter', sans-serif;
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 400;
            max-width: 900px;
            margin: 2rem auto;
            text-align: center;
            line-height: 1.8;
            padding: 0 1rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }
        
        .carousel-container {
            max-width: 1000px;
            margin: 3rem auto;
            padding: 0 1rem;
        }

        .carousel {
            position: relative;
            overflow: hidden;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            background: #fff;
            border: 1px solid var(--border-color);
        }

        .carousel-inner {
            position: relative;
            overflow: hidden;
            height: 450px;
            width: 100%;
        }

        .carousel-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.6s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-item.active {
            opacity: 1;
        }

        .carousel-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .carousel-item:hover img {
            transform: scale(1.02);
        }

        .carousel-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 2rem;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .carousel-item:hover .carousel-overlay {
            transform: translateY(0);
        }

        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            opacity: 0;
            transition: opacity 0.3s ease;
            padding: 0 1.5rem;
            pointer-events: none;
        }

        .carousel:hover .carousel-controls {
            opacity: 1;
            pointer-events: auto;
        }

        .carousel-control {
            background: rgba(255, 255, 255, 0.9);
            color: var(--ongc-blue);
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            border: none;
            font-size: 1.2rem;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .carousel-control:hover {
            background: var(--ongc-blue);
            color: white;
            transform: scale(1.1);
        }

        .carousel-indicators {
            position: absolute;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 0.5rem;
        }

        .carousel-indicator {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-indicator.active {
            background: white;
            transform: scale(1.2);
        }
        
        .button-container {
            text-align: center;
            margin: 4rem 0;
            padding: 0 1rem;
        }

        .main-button {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--ongc-light-blue) 0%, var(--ongc-blue) 100%);
            color: white;
            font-weight: 600;
            padding: 1.25rem 3rem;
            border: none;
            border-radius: 0.75rem;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-lg);
            font-size: 1.125rem;
            margin: 1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            letter-spacing: 0.025em;
            position: relative;
            overflow: hidden;
        }

        .main-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .main-button:hover::before {
            left: 100%;
        }

        .main-button:hover {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, #002244 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            color: white;
            text-decoration: none;
        }

        .main-button:active {
            transform: translateY(0);
        }
        
        .footer {
            background: linear-gradient(135deg, var(--ongc-blue) 0%, #1E293B 100%);
            color: white;
            padding: 3rem 0 2rem;
            text-align: center;
            margin-top: auto;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        }

        .footer a {
            color: var(--ongc-orange);
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .footer a:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .logo-container {
            text-align: center;
            margin: 1.5rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            display: inline-block;
        }

        .logo {
            max-width: 120px;
            height: auto;
            filter: brightness(1.1);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
                padding: 2rem 1rem 1rem;
            }

            .intro-text {
                font-size: 1rem;
                padding: 1.5rem;
            }

            .carousel-inner {
                height: 300px;
            }

            .main-button {
                padding: 1rem 2rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo-container">
                <img src="/static/images/ongc_logo.png" alt="ONGC Logo" class="logo" onerror="this.style.display='none'">
            </div>
            <h1><i class="fas fa-database me-3"></i>SPIC Tape Data Management System</h1>
        </div>
    </header>

    <main>
        <h1 class="main-title">Welcome to SPIC Tape Data Management System</h1>

        <div class="intro-text">
            <p class="mb-0">
                <strong>SeisData Processing and Interpretation Centre</strong> at NBP Green Height, Mumbai is one of the premier Seismic Data Processing centres of ONGC. The Centre processes huge volumes of Seismic data using state-of-the-art Enterprise Software solutions.
            </p>
            <br>
            <p class="mb-0">
                The acquired Seismic Data are received from Operational fields, primarily the Western Offshore Basin, through various Tape Cartridges (3590/3592/LTO etc). These cartridges are professionally managed at SPIC TDMS using advanced systems including TS3500 (E06, E07 drives), TS4500 (E08, 60f drives) and LTO drives to ensure secure and efficient tape management and accurate tracking of shipments to various centres and locations.
            </p>
        </div>

        <div class="carousel-container">
            <div class="carousel">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <img src="/static/images/img1.png" alt="SPIC TDMS Overview" onerror="this.src='https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'">
                        <div class="carousel-overlay">
                            <h3 class="h5 mb-2">Advanced Data Processing</h3>
                            <p class="mb-0">State-of-the-art seismic data processing capabilities</p>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <img src="/static/images/img2.png" alt="Tape Management System" onerror="this.src='https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'">
                        <div class="carousel-overlay">
                            <h3 class="h5 mb-2">Professional Tape Management</h3>
                            <p class="mb-0">Secure and efficient management of tape cartridges</p>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <img src="/static/images/img3.png" alt="Data Center Operations" onerror="this.src='https://images.unsplash.com/photo-1551808525-51a94da548ce?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'">
                        <div class="carousel-overlay">
                            <h3 class="h5 mb-2">Enterprise Solutions</h3>
                            <p class="mb-0">Cutting-edge technology for data interpretation</p>
                        </div>
                    </div>
                </div>
                <div class="carousel-controls">
                    <button class="carousel-control" onclick="prevSlide()" aria-label="Previous slide">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="carousel-control" onclick="nextSlide()" aria-label="Next slide">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="carousel-indicators">
                    <div class="carousel-indicator active" onclick="goToSlide(0)"></div>
                    <div class="carousel-indicator" onclick="goToSlide(1)"></div>
                    <div class="carousel-indicator" onclick="goToSlide(2)"></div>
                </div>
            </div>
        </div>

        <div class="button-container">
            <a href="{% url 'login' %}" class="main-button">
                <i class="fas fa-sign-in-alt"></i>
                Access System
            </a>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="logo-container mb-3">
                        <img src="/static/images/ongc_logo.png" alt="ONGC Logo" class="logo" onerror="this.style.display='none'">
                    </div>
                    <h6 class="mb-3 fw-semibold">
                        <i class="fas fa-database me-2"></i>SPIC Tape Data Management System
                    </h6>
                    <p class="mb-1">SeisData Processing and Interpretation Centre</p>
                    <p class="mb-1">NBP Green Height, Mumbai - ONGC</p>
                    <p class="mb-3">&copy; 2024 Oil and Natural Gas Corporation Limited. All rights reserved.</p>
                    <p class="mb-0">
                        <a href="#" class="text-decoration-none">
                            <i class="fas fa-phone me-1"></i>Technical Support
                        </a>
                        <span class="mx-3">|</span>
                        <a href="#" class="text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>Contact Us
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-item');
        const indicators = document.querySelectorAll('.carousel-indicator');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        function goToSlide(index) {
            currentSlide = index;
            showSlide(currentSlide);
        }

        // Auto slide change every 6 seconds
        let autoSlideInterval = setInterval(nextSlide, 6000);

        // Pause auto-slide on hover
        const carousel = document.querySelector('.carousel');
        carousel.addEventListener('mouseenter', () => {
            clearInterval(autoSlideInterval);
        });

        carousel.addEventListener('mouseleave', () => {
            autoSlideInterval = setInterval(nextSlide, 6000);
        });

        // Add smooth scroll behavior
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>
